import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/subscription/subscription_status_card.dart';
import '../../widgets/subscription/plan_card.dart';

class SubscriptionScreen extends ConsumerStatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  ConsumerState<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends ConsumerState<SubscriptionScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1F1F1), // Main body background
      body: Column(
        children: [
          // Sticky Header
          _buildStickyHeader(),

          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),

                  // Current subscription status
                  const SubscriptionStatusCard(),

                  const SizedBox(height: 24),

                  // Available plans section
                  const Center(
                    child: Text(
                      'Choose Your Plan',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Plan cards
                  const PlanCard(
                    planType: PlanType.basic,
                    title: 'Basic',
                    description: 'Perfect for getting started',
                    price: '₹299',
                    period: '/month',
                    features: [
                      '5 Projects',
                      'Basic Support',
                      '1GB Storage',
                      'Email Integration',
                    ],
                    isCurrentPlan: false,
                    isPopular: false,
                  ),

                  const SizedBox(height: 16),

                  const PlanCard(
                    planType: PlanType.pro,
                    title: 'Pro',
                    description: 'Best for growing businesses',
                    price: '₹599',
                    period: '/month',
                    features: [
                      '25 Projects',
                      'Priority Support',
                      '10GB Storage',
                      'Advanced Analytics',
                      'API Access',
                      'Custom Integrations',
                    ],
                    isCurrentPlan: true,
                    isPopular: true,
                  ),

                  const SizedBox(height: 16),

                  const PlanCard(
                    planType: PlanType.premium,
                    title: 'Enterprise',
                    description: 'For large organizations',
                    price: '₹999',
                    period: '/month',
                    features: [
                      'Unlimited Projects',
                      '24/7 Support',
                      '100GB Storage',
                      'Custom Branding',
                      'Advanced Security',
                      'Dedicated Manager',
                    ],
                    isCurrentPlan: false,
                    isPopular: false,
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStickyHeader() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF1F1F1),
        border: const Border(
          bottom: BorderSide(
            color: Color(0x0F000000),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // Back Button
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.zero,
                  minimumSize: const Size(48, 48),
                ),
                child: const Text(
                  '← Back',
                  style: TextStyle(
                    color: Color(0xFF637488),
                    fontSize: 16,
                  ),
                ),
              ),

              // Title
              const Expanded(
                child: Text(
                  'Subscription',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xFF111418),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Spacer to balance the layout
              const SizedBox(width: 48),
            ],
          ),
        ),
      ),
    );
  }
}
