import 'package:flutter/material.dart';

enum PlanType { basic, pro, premium }

class PlanCard extends StatelessWidget {
  final PlanType planType;
  final String title;
  final String description;
  final String price;
  final String period;
  final String? originalPrice;
  final String? discount;
  final List<String> features;
  final bool isCurrentPlan;
  final bool isPopular;
  final VoidCallback? onPlanSelected;

  const PlanCard({
    super.key,
    required this.planType,
    required this.title,
    required this.description,
    required this.price,
    required this.period,
    this.originalPrice,
    this.discount,
    required this.features,
    this.isCurrentPlan = false,
    this.isPopular = false,
    this.onPlanSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCurrentPlan
              ? const Color(0xFFe92933)
              : const Color(0xFFe0e0e0),
          width: 1,
        ),
        boxShadow: [
          if (isCurrentPlan)
            BoxShadow(
              color: const Color(0xFFe92933).withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            )
          else
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: Stack(
        children: [
          // Badge for popular plan - positioned at top left
          if (isPopular)
            Positioned(
              top: 12,
              left: 12,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFFe92933),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Most Popular',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

          // Current plan tick mark - positioned at top right
          if (isCurrentPlan)
            Positioned(
              top: 12,
              right: 12,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: const Color(0xFFe92933),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),

          // Card content
          Column(
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
                child: Column(
                  children: [
                    // Icon
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: _getIconBackgroundColor(),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: Icon(
                        _getIcon(),
                        color: _getIconColor(),
                        size: 24,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Title
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Description
                    Text(
                      description,
                      style: const TextStyle(
                        color: Color(0xFF637488),
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // Price
                    Column(
                      children: [
                        // Original price (crossed out) if discount available
                        if (originalPrice != null)
                          Text(
                            originalPrice!,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF637488),
                              decoration: TextDecoration.lineThrough,
                            ),
                          ),

                        // Current price
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          textBaseline: TextBaseline.alphabetic,
                          children: [
                            Text(
                              price,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFFe92933),
                              ),
                            ),
                            Text(
                              period,
                              style: const TextStyle(
                                color: Color(0xFF637488),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),

                        // Discount information
                        if (discount != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text(
                              'Save $discount',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF22c55e), // Green color for savings
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),

              // Content
              Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                child: Column(
                  children: [
                    // Features
                    Column(
                      children: features.map((feature) => Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: Row(
                            children: [
                              const Icon(
                                Icons.check,
                                color: Color(0xFFe92933),
                                size: 16,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  feature,
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )).toList(),
                    ),

                    const SizedBox(height: 24),

                    // Action button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isCurrentPlan ? null : onPlanSelected,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isCurrentPlan
                              ? const Color(0xFFe92933)
                              : (isPopular ? const Color(0xFFe92933) : Colors.white),
                          foregroundColor: isCurrentPlan
                              ? Colors.white
                              : (isPopular ? Colors.white : Colors.black),
                          side: !isCurrentPlan && !isPopular
                              ? const BorderSide(color: Color(0xFFe0e0e0))
                              : null,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: Text(
                          isCurrentPlan ? 'Current Plan' : 'Choose $title',
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getIconBackgroundColor() {
    switch (planType) {
      case PlanType.basic:
        return const Color(0xFFf3f4f6); // bg-secondary
      case PlanType.pro:
        return const Color(0x33e92933); // bg-primary/20
      case PlanType.premium:
        return const Color(0xFFf3f4f6); // bg-secondary
    }
  }

  Color _getIconColor() {
    switch (planType) {
      case PlanType.basic:
        return const Color(0xFF637488); // text-muted-foreground
      case PlanType.pro:
        return const Color(0xFFe92933); // text-primary
      case PlanType.premium:
        return const Color(0xFF637488); // text-muted-foreground
    }
  }

  IconData _getIcon() {
    switch (planType) {
      case PlanType.basic:
        return Icons.star;
      case PlanType.pro:
        return Icons.star; // Crown icon would be better but using star for now
      case PlanType.premium:
        return Icons.star;
    }
  }
}
