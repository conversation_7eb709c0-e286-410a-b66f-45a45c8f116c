import 'package:flutter/material.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  String selectedCategory = 'Compliment'; // Default to Compliment
  final TextEditingController _feedbackController = TextEditingController();
  final int maxCharacters = 500;

  final List<String> categories = ['Compliment', 'Feature Request', 'Bug Report'];

  bool get isFormValid {
    return selectedCategory.isNotEmpty &&
           _feedbackController.text.trim().isNotEmpty;
  }

  void _submitFeedback() {
    if (isFormValid) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Thank you for your feedback!',
            textAlign: TextAlign.center,
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
        ),
      );

      // Reset form
      setState(() {
        selectedCategory = 'Compliment';
        _feedbackController.clear();
      });
    }
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1F1F1),
      body: Column(
        children: [
          // Sticky Header
          _buildStickyHeader(),

          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 8),

                  // Welcome Card
                  _buildWelcomeCard(),
                  const SizedBox(height: 24),

                  // Feedback Text Section with Category Selection
                  _buildFeedbackTextSection(),
                  const SizedBox(height: 24),

                  // Submit Button
                  _buildSubmitButton(),
                  const SizedBox(height: 24),

                  // Information Footer
                  _buildInformationFooter(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStickyHeader() {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFFf1f1f1),
        border: Border(
          bottom: BorderSide(
            color: Color(0x0F000000),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // Back Button
              IconButton(
                icon: const Icon(Icons.arrow_back_ios, color: Color(0xFFe92933)),
                onPressed: () => Navigator.pop(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),

              // Title
              const Expanded(
                child: Text(
                  'Feedback',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xFF111418),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Spacer to balance the layout
              const SizedBox(width: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0x33e92933), // bg-primary/20
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.message,
                color: Color(0xFFe92933),
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'We Value Your Feedback',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 4),
            const Text(
              'Help us improve by sharing your thoughts and suggestions',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF637488),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedbackTextSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Feedback',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  'Please share your detailed feedback with us',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF637488),
                  ),
                ),
              ],
            ),
          ),

          // Category Selection
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Category',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF111418),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: categories.map((category) {
                    final isSelected = selectedCategory == category;
                    return Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedCategory = category;
                          });
                        },
                        child: Container(
                          margin: const EdgeInsets.only(right: 8),
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                          decoration: BoxDecoration(
                            color: isSelected ? const Color(0xFFe92933) : Colors.transparent,
                            border: Border.all(
                              color: isSelected ? const Color(0xFFe92933) : const Color(0xFFe0e0e0),
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            category,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: isSelected ? Colors.white : const Color(0xFF637488),
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),

          // Content
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
            child: Column(
              children: [
                TextField(
                  controller: _feedbackController,
                  maxLines: 6,
                  maxLength: maxCharacters,
                  decoration: InputDecoration(
                    hintText: 'Tell us about your experience, suggestions, or any issues you\'ve encountered...',
                    hintStyle: const TextStyle(color: Color(0xFF637488)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFe0e0e0), width: 1),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFe0e0e0), width: 1),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFe92933), width: 2),
                    ),
                    contentPadding: const EdgeInsets.all(12),
                    counterText: '',
                    filled: true,
                    fillColor: const Color(0xFFF1F1F1),
                  ),
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(), // Empty space for alignment
                    Text(
                      '${_feedbackController.text.length}/$maxCharacters characters',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF637488),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: isFormValid ? _submitFeedback : null,
        icon: const Icon(
          Icons.send,
          color: Colors.white,
          size: 16,
        ),
        label: const Text(
          'Submit Feedback',
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: isFormValid ? const Color(0xFFe92933) : const Color(0xFFe0e0e0),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  Widget _buildInformationFooter() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildInfoItem(
              'Your feedback helps us improve our app and provide better service to all users.',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              'For urgent issues, please contact our support team directly.',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              'We typically respond to feedback within 24-48 hours.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 6,
          height: 6,
          margin: const EdgeInsets.only(top: 6),
          decoration: const BoxDecoration(
            color: Color(0xFFe92933),
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
        ),
      ],
    );
  }
}
